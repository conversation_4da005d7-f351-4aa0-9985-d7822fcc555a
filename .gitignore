# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/

# 环境变量和敏感信息
.env
.env.local
.env.*.local
*.key
*.pem
*.p12

# 日志文件
*.log
logs/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Streamlit
.streamlit/

# Jupyter Notebook
.ipynb_checkpoints

# 临时文件
*.tmp
*.temp
.cache/

# 配置备份
config.backup.json
*.backup

# 其他敏感文件
secrets.json
credentials.json
auth.json